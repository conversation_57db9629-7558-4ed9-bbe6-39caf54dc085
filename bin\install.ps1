# install.ps1
# 根据 config.psd1 配置文件安装 dotfiles
# 需要管理员权限来创建符号链接

$dotfilesDir = Split-Path $PSScriptRoot -Parent
$ErrorActionPreference = 'Stop'

# 检查是否通过 setup.cmd 调用
$isCalledFromSetup = $env:DOTFILES_CALLED_FROM_SETUP -eq "1"

# 结果缓存文件路径
$resultCacheFile = Join-Path $env:TEMP "dotfiles_install_result.txt"

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 写入结果到缓存文件
function Write-ResultCache {
    param([string]$Message, [string]$Color = "White")

    if ($isCalledFromSetup) {
        $timestamp = Get-Date -Format "HH:mm:ss"
        $colorCode = switch ($Color) {
            "Green" { "SUCCESS" }
            "Red" { "ERROR" }
            "Yellow" { "WARNING" }
            "Cyan" { "INFO" }
            default { "INFO" }
        }
        "$timestamp [$colorCode] $Message" | Add-Content -Path $resultCacheFile -Encoding UTF8
    }
}

# 输出函数 - 根据调用方式选择不同的输出格式
function Write-InstallOutput {
    param([string]$Message, [string]$Color = "White", [string]$Prefix = "")

    if ($isCalledFromSetup) {
        # 通过 setup.cmd 调用时，写入缓存文件
        Write-ResultCache -Message "$Prefix$Message" -Color $Color
    } else {
        # 直接调用时，根据是否有管理员权限选择输出格式
        if (Test-Administrator) {
            # 管理员终端 - 简洁格式，无emoji
            Write-Host "    $Prefix$Message" -ForegroundColor $Color
        } else {
            # 普通终端 - 保留emoji
            $emojiPrefix = switch ($Color) {
                "Yellow" { if ($Prefix -eq "") { "⚠️ " } else { $Prefix } }
                "Cyan" { if ($Prefix -eq "") { "🔄 " } else { $Prefix } }
                "Red" { if ($Prefix -eq "") { "❌ " } else { $Prefix } }
                "Green" { if ($Prefix -eq "") { "✅ " } else { $Prefix } }
                default { $Prefix }
            }
            Write-Host "$emojiPrefix$Message" -ForegroundColor $Color
        }
    }
}

if (-not (Test-Administrator)) {
    Write-InstallOutput "需要管理员权限创建符号链接" -Color Yellow
    Write-InstallOutput "正在自动提权..." -Color Cyan

    try {
        $scriptPath = $MyInvocation.MyCommand.Path

        # 设置环境变量标识调用来源
        $env:DOTFILES_CALLED_FROM_SETUP = if ($isCalledFromSetup) { "1" } else { "0" }

        $argumentList = @(
            "-NoProfile"
            "-ExecutionPolicy", "Bypass"
            "-Command"
            "& { `$env:DOTFILES_CALLED_FROM_SETUP='$($env:DOTFILES_CALLED_FROM_SETUP)'; & '$scriptPath' }"
        )

        if ($isCalledFromSetup) {
            # 清空之前的结果缓存
            if (Test-Path $resultCacheFile) {
                Remove-Item $resultCacheFile -Force
            }
            Start-Process "PowerShell" -ArgumentList $argumentList -Verb RunAs -Wait -WindowStyle Hidden

            # 读取并显示缓存的结果
            if (Test-Path $resultCacheFile) {
                Write-Host ""
                Write-Host "    ================================================================" -ForegroundColor Green
                Write-Host "      安装结果" -ForegroundColor Yellow
                Write-Host "    ================================================================" -ForegroundColor Green
                Write-Host ""

                $results = Get-Content $resultCacheFile -Encoding UTF8
                foreach ($line in $results) {
                    if ($line -match '^\d{2}:\d{2}:\d{2} \[(SUCCESS|ERROR|WARNING|INFO)\] (.+)$') {
                        $level = $matches[1]
                        $message = $matches[2]
                        $color = switch ($level) {
                            "SUCCESS" { "Green" }
                            "ERROR" { "Red" }
                            "WARNING" { "Yellow" }
                            "INFO" { "Cyan" }
                        }
                        $prefix = switch ($level) {
                            "SUCCESS" { "    [成功] " }
                            "ERROR" { "    [错误] " }
                            "WARNING" { "    [警告] " }
                            "INFO" { "    [信息] " }
                        }
                        Write-Host "$prefix$message" -ForegroundColor $color
                    } else {
                        Write-Host "    $line" -ForegroundColor White
                    }
                }

                Write-Host ""

                # 清理缓存文件
                Remove-Item $resultCacheFile -Force -ErrorAction SilentlyContinue
            } else {
                Write-Host "    [错误] 未找到安装结果" -ForegroundColor Red
            }
        } else {
            Start-Process "PowerShell" -ArgumentList $argumentList -Verb RunAs -Wait -WindowStyle Hidden
        }
        return
    } catch {
        Write-InstallOutput "自动提权失败：$($_.Exception.Message)" -Color Red
        Write-InstallOutput "请手动以管理员身份运行：.\bin\install.ps1" -Color Yellow
        return
    }
}

# 加载配置文件
$configFile = Join-Path $dotfilesDir "config.psd1"
if (-not (Test-Path $configFile)) {
    Write-InstallOutput "配置文件未找到: $configFile" -Color Red
    Write-ResultCache "配置文件未找到: $configFile" -Color "ERROR"
    return
}
$config = Import-PowerShellDataFile -Path $configFile

Write-InstallOutput "开始安装 dotfiles..." -Color Yellow

# 安装前创建备份
Write-InstallOutput "正在创建现有配置的备份..." -Color Cyan
$backupScript = Join-Path $PSScriptRoot "backup.ps1"
if (Test-Path $backupScript) {
    try {
        & $backupScript
        Write-InstallOutput "备份完成" -Color Green
    } catch {
        Write-InstallOutput "备份失败，但继续安装: $($_.Exception.Message)" -Color Yellow
    }
} else {
    Write-InstallOutput "未找到备份脚本，跳过备份" -Color Yellow
}

Write-InstallOutput "正在安装 dotfiles 配置..." -Color Cyan

# 处理配置链接
$successCount = 0
$failureCount = 0

foreach ($link in $config.Links) {
    $sourcePath = Join-Path $dotfilesDir $link.Source
    $targetPath = $link.Target -replace '\\{USERPROFILE\\}', $env:USERPROFILE

    if (-not (Test-Path $sourcePath)) {
        Write-InstallOutput "跳过: 源文件未找到 '$sourcePath'" -Color Yellow
        continue
    }

    $targetDir = Split-Path -Path $targetPath -Parent
    if (-not (Test-Path $targetDir)) {
        Write-InstallOutput "创建目标目录: $targetDir" -Color Gray
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
    }

    # 确定部署方法
    $method = if ($link.Method) { $link.Method } else { $config.DefaultMethod }
    if (-not $method) { $method = "SymLink" }

    try {
        if ($method -eq "Copy") {
            Copy-Item -Path $sourcePath -Destination $targetPath -Force -ErrorAction Stop
            Write-InstallOutput "已复制 $($link.Comment)" -Color Green
        } elseif ($method -eq "Transform") {
            if (-not $link.Transform) {
                Write-InstallOutput "Transform配置缺少Transform参数: $($link.Comment)" -Color Red
                $failureCount++
                continue
            }

            $transformScript = Join-Path $PSScriptRoot "transform.ps1"
            if (-not (Test-Path $transformScript)) {
                Write-InstallOutput "转换脚本未找到: $transformScript" -Color Red
                $failureCount++
                continue
            }

            & $transformScript -SourceFile $sourcePath -TargetFile $targetPath -TransformType $link.Transform -ErrorAction Stop
            Write-InstallOutput "已转换 $($link.Comment)" -Color Green
        } else {
            New-Item -ItemType SymbolicLink -Path $targetPath -Target $sourcePath -Force -ErrorAction Stop | Out-Null
            Write-InstallOutput "已链接 $($link.Comment)" -Color Green
        }
        $successCount++
    } catch {
        Write-InstallOutput "部署失败 $($link.Comment): $($_.Exception.Message)" -Color Red
        if ($method -eq "SymLink") {
            Write-InstallOutput "提示: 创建符号链接需要管理员权限" -Color Yellow
        }
        $failureCount++
    }
}

# 显示结果
Write-InstallOutput "" -Color White
if ($failureCount -eq 0) {
    Write-InstallOutput "Dotfiles 安装完成！" -Color Green
    Write-InstallOutput "安装了 $successCount 个配置" -Color Green
} elseif ($successCount -gt 0) {
    Write-InstallOutput "Dotfiles 安装部分完成（$successCount 成功，$failureCount 失败）" -Color Yellow
} else {
    Write-InstallOutput "Dotfiles 安装失败！" -Color Red
}
Write-InstallOutput "" -Color White