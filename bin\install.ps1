# install.ps1
# 根据 config.psd1 配置文件安装 dotfiles
# 需要管理员权限来创建符号链接

$dotfilesDir = Split-Path $PSScriptRoot -Parent
$ErrorActionPreference = 'Stop'

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "⚠️ 需要管理员权限创建符号链接" -ForegroundColor Yellow
    Write-Host "🔄 正在自动提权..." -ForegroundColor Cyan
    
    try {
        $scriptPath = $MyInvocation.MyCommand.Path
        $argumentList = @(
            "-NoProfile"
            "-ExecutionPolicy", "Bypass"
            "-File", "`"$scriptPath`""
        )
        
        Start-Process "PowerShell" -ArgumentList $argumentList -Verb RunAs -Wait -WindowStyle Hidden
        return
    } catch {
        Write-Host "❌ 自动提权失败：$($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动以管理员身份运行：.\bin\install.ps1" -ForegroundColor Yellow
        return
    }
}

# 加载配置文件
$configFile = Join-Path $dotfilesDir "config.psd1"
if (-not (Test-Path $configFile)) {
    Write-Error "配置文件未找到: $configFile"
    return
}
$config = Import-PowerShellDataFile -Path $configFile

Write-Host "🚀 开始安装 dotfiles..." -ForegroundColor Yellow

# 安装前创建备份
Write-Host "📦 正在创建现有配置的备份..." -ForegroundColor Cyan
$backupScript = Join-Path $PSScriptRoot "backup.ps1"
if (Test-Path $backupScript) {
    try {
        & $backupScript
        Write-Host "✅ 备份完成" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 备份失败，但继续安装: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ 未找到备份脚本，跳过备份" -ForegroundColor Yellow
}

Write-Host "🔗 正在安装 dotfiles 配置..." -ForegroundColor Cyan

# 处理配置链接
$successCount = 0
$failureCount = 0

foreach ($link in $config.Links) {
    $sourcePath = Join-Path $dotfilesDir $link.Source
    $targetPath = $link.Target -replace '\\{USERPROFILE\\}', $env:USERPROFILE

    if (-not (Test-Path $sourcePath)) {
        Write-Host "⚠️ 跳过: 源文件未找到 '$sourcePath'" -ForegroundColor Yellow
        continue
    }

    $targetDir = Split-Path -Path $targetPath -Parent
    if (-not (Test-Path $targetDir)) {
        Write-Host "📁 创建目标目录: $targetDir" -ForegroundColor Gray
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
    }

    # 确定部署方法
    $method = if ($link.Method) { $link.Method } else { $config.DefaultMethod }
    if (-not $method) { $method = "SymLink" }

    try {
        if ($method -eq "Copy") {
            Copy-Item -Path $sourcePath -Destination $targetPath -Force -ErrorAction Stop
            Write-Host "✅ 已复制 $($link.Comment)" -ForegroundColor Green
        } elseif ($method -eq "Transform") {
            if (-not $link.Transform) {
                Write-Host "❌ Transform配置缺少Transform参数: $($link.Comment)" -ForegroundColor Red
                $failureCount++
                continue
            }
            
            $transformScript = Join-Path $PSScriptRoot "transform.ps1"
            if (-not (Test-Path $transformScript)) {
                Write-Host "❌ 转换脚本未找到: $transformScript" -ForegroundColor Red
                $failureCount++
                continue
            }
            
            & $transformScript -SourceFile $sourcePath -TargetFile $targetPath -TransformType $link.Transform -ErrorAction Stop
            Write-Host "✅ 已转换 $($link.Comment)" -ForegroundColor Green
        } else {
            New-Item -ItemType SymbolicLink -Path $targetPath -Target $sourcePath -Force -ErrorAction Stop | Out-Null
            Write-Host "✅ 已链接 $($link.Comment)" -ForegroundColor Green
        }
        $successCount++
    } catch {
        Write-Host "❌ 部署失败 $($link.Comment): $($_.Exception.Message)" -ForegroundColor Red
        if ($method -eq "SymLink") {
            Write-Host "💡 提示: 创建符号链接需要管理员权限" -ForegroundColor Yellow
        }
        $failureCount++
    }
}

# 显示结果
if ($failureCount -eq 0) {
    Write-Host "✨ Dotfiles 安装完成！" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "⚠️ Dotfiles 安装部分完成（$successCount 成功，$failureCount 失败）" -ForegroundColor Yellow
} else {
    Write-Host "❌ Dotfiles 安装失败！" -ForegroundColor Red
}